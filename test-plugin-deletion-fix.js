#!/usr/bin/env node

// Test script to verify the plugin deletion fix for the null pluginSlug error
import fetch from "node-fetch";

const BASE_URL = "http://localhost:5001";

// You'll need to get a valid JWT token from the browser's localStorage or login
const TEST_TOKEN = "your-jwt-token-here"; // Replace with actual token

async function testPluginDeletionFix() {
  console.log("🧪 Testing Plugin Deletion Fix - Complete Flow");
  console.log("=".repeat(70));

  try {
    // Step 1: Get current added plugins to establish baseline
    console.log("\n📋 Step 1: Getting current added plugins...");

    const getResponse = await fetch(`${BASE_URL}/api/plugins/added`, {
      headers: {
        Authorization: `Bearer ${TEST_TOKEN}`,
      },
    });

    if (!getResponse.ok) {
      console.log("❌ Failed to get added plugins:", getResponse.status);
      const errorData = await getResponse.json();
      console.log("Error:", errorData.message);
      return;
    }

    const getResult = await getResponse.json();
    console.log("✅ Successfully retrieved added plugins");
    console.log(`📊 Total plugins: ${getResult.addedPlugins.length}`);

    if (getResult.addedPlugins.length === 0) {
      console.log("⚠️ No plugins found to test deletion");
      return;
    }

    // Show first few plugins
    console.log("\n📦 Available plugins:");
    getResult.addedPlugins.slice(0, 3).forEach((plugin, index) => {
      console.log(
        `${index + 1}. ${plugin.displayName || plugin.pluginName} (slug: ${
          plugin.pluginSlug
        })`
      );
    });

    // Step 2: Delete a plugin to create the problematic scenario
    const testPlugin = getResult.addedPlugins[0];
    console.log(
      `\n🗑️ Step 2: Deleting plugin: ${
        testPlugin.displayName || testPlugin.pluginName
      }`
    );
    console.log(`   Slug: ${testPlugin.pluginSlug}`);

    const deleteResponse = await fetch(
      `${BASE_URL}/api/plugins/added/${testPlugin.pluginSlug}`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${TEST_TOKEN}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!deleteResponse.ok) {
      console.log("❌ Failed to delete plugin:", deleteResponse.status);
      const errorData = await deleteResponse.json();
      console.log("Error:", errorData.message);
      return;
    }

    const deleteResult = await deleteResponse.json();
    console.log(
      "Delete Response:",
      deleteResult.success ? "✅ Success" : "❌ Failed"
    );
    if (deleteResult.message) {
      console.log("Message:", deleteResult.message);
    }

    // Step 3: This is the critical test - fetch plugins again after deletion
    // This should NOT cause the "Cannot read properties of null (reading 'pluginSlug')" error
    // AND the deleted plugin should NOT appear in the list
    console.log(
      "\n🔍 Step 3: Testing the fix - fetching plugins after deletion..."
    );
    console.log("   Testing: 1) No null pluginSlug error");
    console.log("   Testing: 2) Deleted plugin not visible in list");

    const postDeleteResponse = await fetch(`${BASE_URL}/api/plugins/added`, {
      headers: {
        Authorization: `Bearer ${TEST_TOKEN}`,
      },
    });

    if (!postDeleteResponse.ok) {
      console.log(
        "❌ CRITICAL: Failed to fetch plugins after deletion:",
        postDeleteResponse.status
      );
      const errorData = await postDeleteResponse.json();
      console.log("Error:", errorData.message);
      console.log("🚨 This indicates the fix did NOT work!");
      return;
    }

    const postDeleteResult = await postDeleteResponse.json();
    console.log("✅ SUCCESS: Plugin data fetched successfully after deletion!");
    console.log(
      `📊 Plugins count after deletion: ${postDeleteResult.addedPlugins.length}`
    );

    // Step 4: Verify the deleted plugin is not in the list
    const stillExists = postDeleteResult.addedPlugins.find(
      (p) => p.pluginSlug === testPlugin.pluginSlug
    );

    if (stillExists) {
      console.log("❌ Plugin still exists in active list - DELETION FAILED");
      console.log("Plugin data:", stillExists);
    } else {
      console.log("✅ Plugin successfully removed from active list");
    }

    // Step 5: Test multiple fetches to ensure stability
    console.log(
      "\n🔄 Step 4: Testing multiple consecutive fetches for stability..."
    );

    for (let i = 1; i <= 3; i++) {
      console.log(`   Fetch ${i}/3...`);
      const testResponse = await fetch(`${BASE_URL}/api/plugins/added`, {
        headers: {
          Authorization: `Bearer ${TEST_TOKEN}`,
        },
      });

      if (!testResponse.ok) {
        console.log(`❌ Fetch ${i} failed:`, testResponse.status);
        const errorData = await testResponse.json();
        console.log("Error:", errorData.message);
        return;
      }

      const testResult = await testResponse.json();
      console.log(
        `   ✅ Fetch ${i} successful (${testResult.addedPlugins.length} plugins)`
      );
    }

    console.log("\n🎉 ALL TESTS PASSED!");
    console.log("✅ The null pluginSlug error has been successfully fixed!");
    console.log("✅ Plugin deletion works correctly");
    console.log("✅ Plugin data fetching works after deletion");
    console.log("✅ Multiple consecutive fetches are stable");
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
    console.error("Stack trace:", error.stack);
  }
}

// Instructions for getting the JWT token
console.log("📝 Instructions:");
console.log("1. Open the browser and login to the dashboard");
console.log("2. Open browser DevTools (F12)");
console.log("3. Go to Application/Storage tab > Local Storage");
console.log('4. Copy the "token" value');
console.log("5. Replace TEST_TOKEN in this script with the actual token");
console.log("6. Make sure the backend server is running on localhost:5001");
console.log("7. Run this script: node test-plugin-deletion-fix.js");
console.log("");

// Uncomment the line below and add your token to run the test
// testPluginDeletionFix();
